# Energy Material Python API

基于Flask的HTTP接口服务，提供echo测试接口和其他API功能。

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务器

```bash
python flask_api.py
```

服务器将在 `http://localhost:5000` 启动。

### 3. 测试接口

运行测试脚本：
```bash
python test_api.py
```

## API接口文档

### 1. 根路径 - API信息
- **URL**: `/`
- **方法**: GET
- **描述**: 获取API基本信息和可用接口列表
- **响应示例**:
```json
{
  "message": "Energy Material Python API Server",
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00",
  "endpoints": [...]
}
```

### 2. Echo测试接口
- **URL**: `/echo`
- **方法**: GET, POST
- **描述**: 回显请求参数和数据，用于测试API连通性

#### GET请求示例
```bash
curl "http://localhost:5000/echo?name=test&value=123"
```

响应：
```json
{
  "method": "GET",
  "timestamp": "2024-01-01T12:00:00",
  "client_ip": "127.0.0.1",
  "query_params": {
    "name": "test",
    "value": "123"
  },
  "message": "Echo GET request received"
}
```

#### POST请求示例 (JSON)
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"key": "value", "number": 42}' \
  "http://localhost:5000/echo?source=test"
```

响应：
```json
{
  "method": "POST",
  "timestamp": "2024-01-01T12:00:00",
  "client_ip": "127.0.0.1",
  "query_params": {
    "source": "test"
  },
  "request_body": {
    "key": "value",
    "number": 42
  },
  "content_type": "application/json",
  "message": "Echo POST request received"
}
```

#### POST请求示例 (表单数据)
```bash
curl -X POST \
  -d "username=testuser&email=<EMAIL>" \
  "http://localhost:5000/echo"
```

### 3. 健康检查接口
- **URL**: `/health`
- **方法**: GET
- **描述**: 检查服务器健康状态
- **响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00",
  "service": "Energy Material Python API"
}
```

## 使用Python requests测试

```python
import requests
import json

# GET请求
response = requests.get('http://localhost:5000/echo', 
                       params={'name': 'test', 'value': '123'})
print(response.json())

# POST请求 (JSON)
data = {'message': 'Hello', 'data': {'temp': 25.5}}
response = requests.post('http://localhost:5000/echo', json=data)
print(response.json())

# POST请求 (表单)
form_data = {'username': 'testuser', 'email': '<EMAIL>'}
response = requests.post('http://localhost:5000/echo', data=form_data)
print(response.json())
```

## 错误处理

API包含完整的错误处理机制：

- **404 Not Found**: 请求的接口不存在
- **500 Internal Server Error**: 服务器内部错误

错误响应格式：
```json
{
  "error": "错误类型",
  "message": "错误描述",
  "timestamp": "2024-01-01T12:00:00"
}
```

## 配置说明

- **主机**: 0.0.0.0 (监听所有网络接口)
- **端口**: 5000
- **调试模式**: 开启 (开发环境)
- **多线程**: 支持
- **中文支持**: 完整支持中文JSON响应

## 扩展开发

要添加新的API接口，在 `flask_api.py` 中添加新的路由函数：

```python
@app.route('/your-endpoint', methods=['GET', 'POST'])
def your_function():
    # 处理逻辑
    return jsonify({'result': 'success'})
```

## 注意事项

1. 当前配置为开发模式，生产环境请使用WSGI服务器（如Gunicorn）
2. 默认监听所有网络接口，请根据安全需求调整
3. 日志记录已配置，可在控制台查看请求日志
