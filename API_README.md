# Energy Material Python API

基于Flask的HTTP接口服务，提供echo测试接口和其他API功能。

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务器

#### 开发环境 (推荐用于开发和测试)
```bash
# 方式1: 直接运行Flask应用
python flask_api.py

# 方式2: 使用启动脚本 (推荐)
python run_server.py

# 方式3: 指定开发环境
python run_server.py --env development
```

#### 生产环境 (推荐用于部署)
```bash
# 方式1: 使用启动脚本自动检测
FLASK_ENV=production python run_server.py

# 方式2: 强制使用Gunicorn
python run_server.py --server gunicorn

# 方式3: 直接使用Gunicorn
gunicorn -c gunicorn.conf.py flask_api:app
```

服务器将在 `http://localhost:10010` 启动。

### 3. 测试接口

运行测试脚本：
```bash
python test_api.py
```

## API接口文档

### 1. 根路径 - API信息
- **URL**: `/`
- **方法**: GET
- **描述**: 获取API基本信息和可用接口列表
- **响应示例**:
```json
{
  "message": "Energy Material Python API Server",
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00",
  "endpoints": [...]
}
```

### 2. Echo测试接口
- **URL**: `/echo`
- **方法**: GET, POST
- **描述**: 回显请求参数和数据，用于测试API连通性

#### GET请求示例
```bash
curl "http://localhost:5000/echo?name=test&value=123"
```

响应：
```json
{
  "method": "GET",
  "timestamp": "2024-01-01T12:00:00",
  "client_ip": "127.0.0.1",
  "query_params": {
    "name": "test",
    "value": "123"
  },
  "message": "Echo GET request received"
}
```

#### POST请求示例 (JSON)
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"key": "value", "number": 42}' \
  "http://localhost:5000/echo?source=test"
```

响应：
```json
{
  "method": "POST",
  "timestamp": "2024-01-01T12:00:00",
  "client_ip": "127.0.0.1",
  "query_params": {
    "source": "test"
  },
  "request_body": {
    "key": "value",
    "number": 42
  },
  "content_type": "application/json",
  "message": "Echo POST request received"
}
```

#### POST请求示例 (表单数据)
```bash
curl -X POST \
  -d "username=testuser&email=<EMAIL>" \
  "http://localhost:5000/echo"
```

### 3. 健康检查接口
- **URL**: `/health`
- **方法**: GET
- **描述**: 检查服务器健康状态
- **响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00",
  "service": "Energy Material Python API"
}
```

## 使用Python requests测试

```python
import requests
import json

# GET请求
response = requests.get('http://localhost:5000/echo', 
                       params={'name': 'test', 'value': '123'})
print(response.json())

# POST请求 (JSON)
data = {'message': 'Hello', 'data': {'temp': 25.5}}
response = requests.post('http://localhost:5000/echo', json=data)
print(response.json())

# POST请求 (表单)
form_data = {'username': 'testuser', 'email': '<EMAIL>'}
response = requests.post('http://localhost:5000/echo', data=form_data)
print(response.json())
```

## 错误处理

API包含完整的错误处理机制：

- **404 Not Found**: 请求的接口不存在
- **500 Internal Server Error**: 服务器内部错误

错误响应格式：
```json
{
  "error": "错误类型",
  "message": "错误描述",
  "timestamp": "2024-01-01T12:00:00"
}
```

## 配置说明

- **主机**: 0.0.0.0 (监听所有网络接口)
- **端口**: 5000
- **调试模式**: 开启 (开发环境)
- **多线程**: 支持
- **中文支持**: 完整支持中文JSON响应

## 扩展开发

要添加新的API接口，在 `flask_api.py` 中添加新的路由函数：

```python
@app.route('/your-endpoint', methods=['GET', 'POST'])
def your_function():
    # 处理逻辑
    return jsonify({'result': 'success'})
```

## 环境配置

### 环境变量

创建 `.env` 文件来配置环境变量（参考 `.env.example`）：

```bash
# 复制示例配置文件
cp .env.example .env

# 编辑配置
# 修改 .env 文件中的配置项
```

主要配置项：
- `FLASK_ENV`: 运行环境 (development/production/testing)
- `FLASK_HOST`: 服务器主机地址 (默认: 0.0.0.0)
- `FLASK_PORT`: 服务器端口 (默认: 10010)
- `SECRET_KEY`: Flask密钥 (生产环境必须修改)
- `LOG_LEVEL`: 日志级别 (DEBUG/INFO/WARNING/ERROR)

### 查看当前配置

```bash
python run_server.py --config
```

## 生产部署

### 使用Gunicorn部署

1. **安装Gunicorn** (已包含在requirements.txt中):
```bash
pip install gunicorn
```

2. **启动生产服务器**:
```bash
# 设置生产环境
export FLASK_ENV=production

# 启动Gunicorn服务器
python run_server.py
# 或直接使用
gunicorn -c gunicorn.conf.py flask_api:app
```

3. **Gunicorn配置**:
   - 配置文件: `gunicorn.conf.py`
   - 工作进程数: 4 (可通过环境变量调整)
   - 工作进程类型: sync (可选: gevent, eventlet)
   - 超时时间: 30秒
   - 自动重启: 支持

### 使用systemd服务 (Linux)

创建服务文件 `/etc/systemd/system/energy-api.service`:

```ini
[Unit]
Description=Energy Material Python API
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/energy-mat2py
Environment=FLASK_ENV=production
ExecStart=/path/to/venv/bin/python run_server.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启动服务:
```bash
sudo systemctl enable energy-api
sudo systemctl start energy-api
sudo systemctl status energy-api
```

### 使用Docker部署

创建 `Dockerfile`:
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 10010

ENV FLASK_ENV=production
CMD ["python", "run_server.py"]
```

构建和运行:
```bash
docker build -t energy-api .
docker run -p 10010:10010 -e FLASK_ENV=production energy-api
```

## 注意事项

1. **开发环境**: 使用Flask内置服务器，支持自动重载和调试
2. **生产环境**: 使用Gunicorn WSGI服务器，提供更好的性能和稳定性
3. **安全配置**: 生产环境请修改SECRET_KEY并考虑使用HTTPS
4. **日志记录**: 已配置完整的日志系统，可在控制台查看请求日志
5. **性能调优**: Gunicorn配置已针对一般场景优化，可根据需要调整
