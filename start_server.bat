@echo off
REM Windows批处理脚本 - 启动Energy Material Python API服务器
REM 使用方法: start_server.bat [development|production]

echo ============================================================
echo Energy Material Python API Server - Windows启动脚本
echo ============================================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或未添加到PATH环境变量
    echo 请安装Python并确保可以在命令行中运行python命令
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 设置环境变量
if "%1"=="production" (
    echo 设置生产环境...
    set FLASK_ENV=production
) else (
    echo 设置开发环境...
    set FLASK_ENV=development
)

REM 显示配置信息
echo.
echo 当前配置:
python run_server.py --config

echo.
echo ============================================================
echo 启动服务器...
echo 按 Ctrl+C 停止服务器
echo ============================================================

REM 启动服务器
python run_server.py

echo.
echo 服务器已停止
pause
