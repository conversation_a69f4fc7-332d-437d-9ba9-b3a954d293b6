#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器启动脚本
根据环境自动选择开发服务器或生产服务器(Gunicorn)
"""

import os
import sys
import subprocess
import argparse
from config import get_config, print_config_info, DevelopmentConfig, ProductionConfig


def run_development_server():
    """启动开发服务器"""
    print("🚀 启动Flask开发服务器...")
    from flask_api import app
    config = DevelopmentConfig()
    server_config = config.get_server_config()
    
    print(f"服务器地址: http://{config.HOST}:{config.PORT}")
    print("开发模式: 自动重载已启用")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        app.run(**server_config)
    except KeyboardInterrupt:
        print("\n服务器已停止")


def run_production_server():
    """启动生产服务器(Gunicorn)"""
    print("🏭 启动Gunicorn生产服务器...")
    
    # 检查Gunicorn是否安装
    try:
        import gunicorn
    except ImportError:
        print("❌ 错误: Gunicorn未安装")
        print("请运行: pip install gunicorn")
        sys.exit(1)
    
    config = ProductionConfig()
    print(f"服务器地址: http://{config.HOST}:{config.PORT}")
    print(f"工作进程数: {config.WORKERS}")
    print(f"工作进程类型: {config.WORKER_CLASS}")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    # 构建Gunicorn命令
    cmd = [
        'gunicorn',
        '-c', 'gunicorn.conf.py',
        'flask_api:app'
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ Gunicorn启动失败: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print("❌ 错误: 找不到gunicorn命令")
        print("请确保Gunicorn已正确安装: pip install gunicorn")
        sys.exit(1)


def run_gunicorn_direct():
    """直接使用Gunicorn Python API启动"""
    print("🏭 使用Gunicorn Python API启动...")
    
    try:
        from gunicorn.app.base import BaseApplication
        from flask_api import app
        
        class StandaloneApplication(BaseApplication):
            def __init__(self, app, options=None):
                self.options = options or {}
                self.application = app
                super().__init__()
            
            def load_config(self):
                config = {key: value for key, value in self.options.items()
                         if key in self.cfg.settings and value is not None}
                for key, value in config.items():
                    self.cfg.set(key.lower(), value)
            
            def load(self):
                return self.application
        
        # 获取Gunicorn配置
        prod_config = ProductionConfig()
        options = prod_config.get_gunicorn_config()
        
        print(f"服务器地址: {options['bind']}")
        print(f"工作进程数: {options['workers']}")
        print("按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        StandaloneApplication(app, options).run()
        
    except ImportError:
        print("❌ 错误: Gunicorn未安装")
        print("请运行: pip install gunicorn")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n服务器已停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Energy Material Python API Server')
    parser.add_argument(
        '--env', 
        choices=['development', 'production', 'auto'],
        default='auto',
        help='运行环境 (默认: auto - 根据FLASK_ENV环境变量自动检测)'
    )
    parser.add_argument(
        '--server',
        choices=['flask', 'gunicorn', 'gunicorn-api'],
        help='强制指定服务器类型'
    )
    parser.add_argument(
        '--config',
        action='store_true',
        help='显示配置信息并退出'
    )
    
    args = parser.parse_args()
    
    # 设置环境变量
    if args.env != 'auto':
        os.environ['FLASK_ENV'] = args.env
    
    # 获取配置
    config = get_config()
    
    # 显示配置信息
    if args.config:
        print("=" * 60)
        print("配置信息")
        print("=" * 60)
        print_config_info(config)
        return
    
    print("=" * 60)
    print("Energy Material Python API Server")
    print("=" * 60)
    print_config_info(config)
    print("=" * 60)
    
    # 确定服务器类型
    env = os.environ.get('FLASK_ENV', 'development')
    
    if args.server:
        server_type = args.server
    elif env == 'production':
        server_type = 'gunicorn'
    else:
        server_type = 'flask'
    
    # 启动相应的服务器
    if server_type == 'flask':
        run_development_server()
    elif server_type == 'gunicorn':
        run_production_server()
    elif server_type == 'gunicorn-api':
        run_gunicorn_direct()
    else:
        print(f"❌ 未知的服务器类型: {server_type}")
        sys.exit(1)


if __name__ == '__main__':
    main()
