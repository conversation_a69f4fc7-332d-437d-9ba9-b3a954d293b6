# 环境变量配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# Flask环境配置
FLASK_ENV=development
# 可选值: development, production, testing

# 服务器配置
FLASK_HOST=0.0.0.0
FLASK_PORT=10010

# 安全配置
SECRET_KEY=your-secret-key-here-change-in-production

# 日志配置
LOG_LEVEL=INFO
# 可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL

# Gunicorn生产服务器配置
GUNICORN_WORKERS=4
GUNICORN_WORKER_CLASS=sync
# 可选值: sync, gevent, eventlet, tornado

GUNICORN_WORKER_CONNECTIONS=1000
GUNICORN_MAX_REQUESTS=1000
GUNICORN_MAX_REQUESTS_JITTER=100
GUNICORN_TIMEOUT=30
GUNICORN_KEEPALIVE=2

# SSL配置 (可选)
# SSL_KEYFILE=/path/to/keyfile.key
# SSL_CERTFILE=/path/to/certfile.crt
