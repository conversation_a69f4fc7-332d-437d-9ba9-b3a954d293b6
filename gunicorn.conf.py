#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gunicorn配置文件
用于生产环境部署
"""

import os
import multiprocessing
from config import ProductionConfig

# 获取生产配置
prod_config = ProductionConfig()

# 服务器绑定
bind = f"{prod_config.HOST}:{prod_config.PORT}"

# 工作进程配置
workers = prod_config.WORKERS
worker_class = prod_config.WORKER_CLASS
worker_connections = prod_config.WORKER_CONNECTIONS

# 请求处理配置
max_requests = prod_config.MAX_REQUESTS
max_requests_jitter = prod_config.MAX_REQUESTS_JITTER
timeout = prod_config.TIMEOUT
keepalive = prod_config.KEEPALIVE

# 应用配置
preload_app = True
reload = False

# 日志配置
accesslog = '-'  # 输出到stdout
errorlog = '-'   # 输出到stderr
loglevel = prod_config.LOG_LEVEL.lower()
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程管理
daemon = False
pidfile = '/tmp/gunicorn.pid'
user = None
group = None
tmp_upload_dir = None

# 安全配置
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# SSL配置（如果需要）
keyfile = os.environ.get('SSL_KEYFILE')
certfile = os.environ.get('SSL_CERTFILE')

# 性能调优
worker_tmp_dir = '/dev/shm'  # 使用内存文件系统提高性能

# 钩子函数
def on_starting(server):
    """服务器启动时调用"""
    server.log.info("Gunicorn服务器正在启动...")

def on_reload(server):
    """重载时调用"""
    server.log.info("Gunicorn服务器正在重载...")

def when_ready(server):
    """服务器准备就绪时调用"""
    server.log.info(f"Gunicorn服务器已启动，监听 {bind}")
    server.log.info(f"工作进程数: {workers}")
    server.log.info(f"工作进程类型: {worker_class}")

def worker_int(worker):
    """工作进程收到SIGINT信号时调用"""
    worker.log.info(f"工作进程 {worker.pid} 收到中断信号")

def pre_fork(server, worker):
    """fork工作进程前调用"""
    server.log.info(f"正在启动工作进程 {worker.pid}")

def post_fork(server, worker):
    """fork工作进程后调用"""
    server.log.info(f"工作进程 {worker.pid} 已启动")

def worker_abort(worker):
    """工作进程异常退出时调用"""
    worker.log.error(f"工作进程 {worker.pid} 异常退出")

def on_exit(server):
    """服务器退出时调用"""
    server.log.info("Gunicorn服务器正在关闭...")
