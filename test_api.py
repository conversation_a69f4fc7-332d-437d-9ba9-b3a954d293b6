#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask API 测试脚本
用于测试flask_api.py中的各个接口
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:5000"

def test_api():
    """
    测试所有API接口
    """
    print("=" * 50)
    print("Flask API 接口测试")
    print("=" * 50)
    
    try:
        # 1. 测试根路径
        print("\n1. 测试根路径 GET /")
        response = requests.get(f"{BASE_URL}/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 2. 测试健康检查
        print("\n2. 测试健康检查 GET /health")
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 3. 测试Echo GET请求
        print("\n3. 测试Echo GET请求")
        params = {"name": "测试用户", "age": "25", "city": "北京"}
        response = requests.get(f"{BASE_URL}/echo", params=params)
        print(f"状态码: {response.status_code}")
        print(f"请求参数: {params}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 4. 测试Echo POST请求 (JSON)
        print("\n4. 测试Echo POST请求 (JSON)")
        json_data = {
            "message": "这是一个测试消息",
            "data": {
                "temperature": 25.5,
                "humidity": 60,
                "location": "实验室"
            },
            "timestamp": time.time()
        }
        params = {"source": "test_script", "version": "1.0"}
        response = requests.post(
            f"{BASE_URL}/echo",
            json=json_data,
            params=params
        )
        print(f"状态码: {response.status_code}")
        print(f"请求JSON: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
        print(f"请求参数: {params}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 5. 测试Echo POST请求 (表单数据)
        print("\n5. 测试Echo POST请求 (表单数据)")
        form_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "message": "表单测试消息"
        }
        response = requests.post(f"{BASE_URL}/echo", data=form_data)
        print(f"状态码: {response.status_code}")
        print(f"表单数据: {form_data}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 6. 测试不存在的接口
        print("\n6. 测试不存在的接口 GET /nonexistent")
        response = requests.get(f"{BASE_URL}/nonexistent")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败！请确保Flask服务器正在运行。")
        print("启动服务器命令: python flask_api.py")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")

def test_echo_variations():
    """
    测试Echo接口的各种变化
    """
    print("\n" + "=" * 50)
    print("Echo接口详细测试")
    print("=" * 50)
    
    try:
        # 测试空参数
        print("\n1. 测试空参数")
        response = requests.get(f"{BASE_URL}/echo")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 测试中文参数
        print("\n2. 测试中文参数")
        params = {"姓名": "张三", "城市": "上海", "职业": "工程师"}
        response = requests.get(f"{BASE_URL}/echo", params=params)
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 测试特殊字符
        print("\n3. 测试特殊字符")
        params = {"special": "!@#$%^&*()", "symbols": "αβγδε", "emoji": "😀🚀🔬"}
        response = requests.get(f"{BASE_URL}/echo", params=params)
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    print("开始测试Flask API...")
    print("请确保Flask服务器已启动 (python flask_api.py)")
    input("按Enter键开始测试...")
    
    test_api()
    test_echo_variations()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)
