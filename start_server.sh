#!/bin/bash
# Linux/macOS启动脚本 - Energy Material Python API服务器
# 使用方法: ./start_server.sh [development|production]

echo "============================================================"
echo "Energy Material Python API Server - Linux/macOS启动脚本"
echo "============================================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: Python3未安装"
    echo "请安装Python3"
    exit 1
fi

# 检查依赖是否安装
echo "检查依赖..."
python3 -c "import flask" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装依赖..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖安装失败"
        exit 1
    fi
fi

# 设置环境变量
if [ "$1" = "production" ]; then
    echo "设置生产环境..."
    export FLASK_ENV=production
else
    echo "设置开发环境..."
    export FLASK_ENV=development
fi

# 显示配置信息
echo ""
echo "当前配置:"
python3 run_server.py --config

echo ""
echo "============================================================"
echo "启动服务器..."
echo "按 Ctrl+C 停止服务器"
echo "============================================================"

# 启动服务器
python3 run_server.py

echo ""
echo "服务器已停止"
