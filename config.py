#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask应用配置文件
支持开发环境和生产环境的不同配置
"""

import os
from typing import Dict, Any


class Config:
    """基础配置类"""
    # Flask基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    JSON_AS_ASCII = False  # 支持中文JSON响应
    
    # 服务器配置
    HOST = os.environ.get('FLASK_HOST') or '0.0.0.0'
    PORT = int(os.environ.get('FLASK_PORT') or 10010)
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # API配置
    API_VERSION = '1.0.0'
    API_TITLE = 'Energy Material Python API Server'


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False
    
    # 开发服务器配置
    USE_RELOADER = True
    THREADED = True
    
    # 日志级别
    LOG_LEVEL = 'DEBUG'
    
    @staticmethod
    def get_server_config() -> Dict[str, Any]:
        """获取开发服务器配置"""
        return {
            'host': DevelopmentConfig.HOST,
            'port': DevelopmentConfig.PORT,
            'debug': DevelopmentConfig.DEBUG,
            'use_reloader': DevelopmentConfig.USE_RELOADER,
            'threaded': DevelopmentConfig.THREADED
        }


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False
    
    # Gunicorn配置
    WORKERS = int(os.environ.get('GUNICORN_WORKERS') or 4)
    WORKER_CLASS = os.environ.get('GUNICORN_WORKER_CLASS') or 'sync'
    WORKER_CONNECTIONS = int(os.environ.get('GUNICORN_WORKER_CONNECTIONS') or 1000)
    MAX_REQUESTS = int(os.environ.get('GUNICORN_MAX_REQUESTS') or 1000)
    MAX_REQUESTS_JITTER = int(os.environ.get('GUNICORN_MAX_REQUESTS_JITTER') or 100)
    TIMEOUT = int(os.environ.get('GUNICORN_TIMEOUT') or 30)
    KEEPALIVE = int(os.environ.get('GUNICORN_KEEPALIVE') or 2)
    
    # 安全配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or os.urandom(32).hex()
    
    @staticmethod
    def get_gunicorn_config() -> Dict[str, Any]:
        """获取Gunicorn配置"""
        return {
            'bind': f"{ProductionConfig.HOST}:{ProductionConfig.PORT}",
            'workers': ProductionConfig.WORKERS,
            'worker_class': ProductionConfig.WORKER_CLASS,
            'worker_connections': ProductionConfig.WORKER_CONNECTIONS,
            'max_requests': ProductionConfig.MAX_REQUESTS,
            'max_requests_jitter': ProductionConfig.MAX_REQUESTS_JITTER,
            'timeout': ProductionConfig.TIMEOUT,
            'keepalive': ProductionConfig.KEEPALIVE,
            'preload_app': True,
            'access_logfile': '-',  # 输出到stdout
            'error_logfile': '-',   # 输出到stderr
            'log_level': ProductionConfig.LOG_LEVEL.lower(),
            'capture_output': True
        }


class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = True
    TESTING = True
    PORT = 5001  # 使用不同端口避免冲突


# 配置映射
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}


def get_config(config_name: str = None) -> Config:
    """
    获取配置对象
    
    Args:
        config_name: 配置名称 ('development', 'production', 'testing')
                    如果为None，则从环境变量FLASK_ENV获取
    
    Returns:
        配置对象
    """
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    return config_map.get(config_name, config_map['default'])


def print_config_info(config: Config):
    """打印配置信息"""
    print(f"配置类型: {config.__class__.__name__}")
    print(f"调试模式: {config.DEBUG}")
    print(f"主机地址: {config.HOST}")
    print(f"端口号: {config.PORT}")
    print(f"日志级别: {config.LOG_LEVEL}")
    
    if isinstance(config, ProductionConfig):
        print(f"工作进程数: {config.WORKERS}")
        print(f"工作进程类型: {config.WORKER_CLASS}")
        print(f"超时时间: {config.TIMEOUT}秒")
