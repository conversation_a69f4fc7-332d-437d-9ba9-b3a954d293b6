#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask HTTP API Server
提供HTTP接口服务，包含echo测试接口
"""

from flask import Flask, request, jsonify
import json
import os
import sys
from datetime import datetime
import logging
from config import get_config, print_config_info

# 获取配置
config = get_config()

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)

# 应用配置
app.config.from_object(config)


@app.route('/', methods=['GET'])
def index():
    """
    根路径，返回API信息
    """
    return jsonify({
        'message': config.API_TITLE,
        'version': config.API_VERSION,
        'environment': os.environ.get('FLASK_ENV', 'development'),
        'timestamp': datetime.now().isoformat(),
        'endpoints': [
            {'path': '/', 'method': 'GET', 'description': 'API信息'},
            {'path': '/echo', 'method': 'GET', 'description': 'Echo测试接口 - GET方式'},
            {'path': '/echo', 'method': 'POST', 'description': 'Echo测试接口 - POST方式'},
            {'path': '/health', 'method': 'GET', 'description': '健康检查接口'}
        ]
    })


@app.route('/echo', methods=['GET', 'POST'])
def echo():
    """
    Echo测试接口
    GET: 返回查询参数
    POST: 返回请求体内容和查询参数
    """
    try:
        response_data = {
            'method': request.method,
            'timestamp': datetime.now().isoformat(),
            'client_ip': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', ''),
        }
        
        if request.method == 'GET':
            # GET请求：返回查询参数
            response_data.update({
                'query_params': dict(request.args),
                'message': 'Echo GET request received'
            })
            
        elif request.method == 'POST':
            # POST请求：返回请求体和查询参数
            content_type = request.headers.get('Content-Type', '')
            
            if 'application/json' in content_type:
                try:
                    request_body = request.get_json()
                except Exception as e:
                    request_body = {'error': f'Invalid JSON: {str(e)}'}
            else:
                # 处理表单数据或其他格式
                request_body = dict(request.form) if request.form else request.get_data(as_text=True)
            
            response_data.update({
                'query_params': dict(request.args),
                'request_body': request_body,
                'content_type': content_type,
                'message': 'Echo POST request received'
            })
        
        logger.info(f"Echo request processed: {request.method} from {request.remote_addr}")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error processing echo request: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


@app.route('/health', methods=['GET'])
def health_check():
    """
    健康检查接口
    """
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'Energy Material Python API'
    })


@app.errorhandler(404)
def not_found(error):
    """
    404错误处理
    """
    return jsonify({
        'error': 'Not Found',
        'message': 'The requested endpoint does not exist',
        'timestamp': datetime.now().isoformat()
    }), 404


@app.errorhandler(500)
def internal_error(error):
    """
    500错误处理
    """
    return jsonify({
        'error': 'Internal Server Error',
        'message': 'An internal server error occurred',
        'timestamp': datetime.now().isoformat()
    }), 500


def create_app():
    """
    应用工厂函数，用于Gunicorn等WSGI服务器
    """
    return app


if __name__ == '__main__':
    # 打印配置信息
    print("=" * 60)
    print("Energy Material Python API Server")
    print("=" * 60)
    print_config_info(config)
    print("=" * 60)

    print("\nAvailable endpoints:")
    print("  GET  /          - API信息")
    print("  GET  /echo      - Echo测试接口 (GET)")
    print("  POST /echo      - Echo测试接口 (POST)")
    print("  GET  /health    - 健康检查")

    print(f"\nExample usage:")
    print(f"  curl http://{config.HOST}:{config.PORT}/")
    print(f"  curl http://{config.HOST}:{config.PORT}/echo?name=test&value=123")
    print(f"  curl -X POST -H 'Content-Type: application/json' -d '{{\"key\":\"value\"}}' http://{config.HOST}:{config.PORT}/echo")

    # 根据配置决定启动方式
    env = os.environ.get('FLASK_ENV', 'development')

    if env == 'production':
        print(f"\n⚠️  生产环境检测到！")
        print(f"建议使用Gunicorn启动:")
        print(f"  gunicorn -c gunicorn.conf.py flask_api:app")
        print(f"或者:")
        print(f"  python run_server.py")
        print(f"\n如果仍要使用开发服务器，请设置 FLASK_ENV=development")

        # 生产环境下仍可直接运行，但会给出警告
        from config import DevelopmentConfig
        dev_config = DevelopmentConfig.get_server_config()
        print(f"\n⚠️  警告：正在生产环境中使用开发服务器！")
        print(f"服务器启动在 http://{config.HOST}:{config.PORT}")
        app.run(**dev_config)
    else:
        # 开发环境
        from config import DevelopmentConfig
        server_config = DevelopmentConfig.get_server_config()
        print(f"\n🚀 开发服务器启动在 http://{config.HOST}:{config.PORT}")
        app.run(**server_config)
